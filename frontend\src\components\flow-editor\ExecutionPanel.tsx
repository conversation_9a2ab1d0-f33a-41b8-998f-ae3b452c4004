import { useState, useEffect, useRef } from 'react'
import { FlowExecution, ExecutionLog } from '@rpa-project/shared'
import { executionApi } from '../../services/api'

interface ExecutionPanelProps {
  isOpen: boolean
  onClose: () => void
  execution: FlowExecution | null
}

export function ExecutionPanel({ isOpen, onClose, execution }: ExecutionPanelProps) {
  const [logs, setLogs] = useState<ExecutionLog[]>([])
  const [loading, setLoading] = useState(false)
  const [cancelling, setCancelling] = useState(false)
  const logsEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new logs arrive
  const scrollToBottom = () => {
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [logs])

  // Poll for logs when execution is running
  useEffect(() => {
    if (!execution || !isOpen) {
      setLogs([])
      return
    }

    const loadLogs = async () => {
      try {
        setLoading(true)
        const response = await executionApi.getExecutionLogs(execution.id)
        if (response.success) {
          setLogs(response.data || [])
        }
      } catch (error) {
        console.error('Failed to load logs:', error)
      } finally {
        setLoading(false)
      }
    }

    // Load initial logs
    loadLogs()

    // Poll for updates if execution is still running
    const interval = setInterval(() => {
      if (execution.status === 'running' || execution.status === 'pending') {
        loadLogs()
      }
    }, 1000) // Poll every second

    return () => clearInterval(interval)
  }, [execution, isOpen])

  const handleCancel = async () => {
    if (!execution || cancelling) return

    try {
      setCancelling(true)
      await executionApi.cancelExecution(execution.id)
      // The execution status will be updated through polling
    } catch (error) {
      console.error('Failed to cancel execution:', error)
      alert('Failed to cancel execution')
    } finally {
      setCancelling(false)
    }
  }

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'error': return '#ef4444'
      case 'warn': return '#f59e0b'
      case 'info': return '#3b82f6'
      case 'debug': return '#6b7280'
      default: return '#6b7280'
    }
  }

  const getLogLevelBadge = (level: string) => {
    switch (level) {
      case 'error': return '❌'
      case 'warn': return '⚠️'
      case 'info': return 'ℹ️'
      case 'debug': return '🔍'
      default: return '📝'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#10b981'
      case 'failed': return '#ef4444'
      case 'cancelled': return '#6b7280'
      case 'running': return '#3b82f6'
      case 'pending': return '#f59e0b'
      default: return '#6b7280'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✅'
      case 'failed': return '❌'
      case 'cancelled': return '⏹️'
      case 'running': return '🔄'
      case 'pending': return '⏳'
      default: return '❓'
    }
  }

  const formatTimestamp = (timestamp: Date) => {
    return new Date(timestamp).toLocaleTimeString('sv-SE', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  return (
    <>
      {/* Overlay */}
      {isOpen && (
        <div
          className="modal-overlay"
          onClick={onClose}
        />
      )}

      {/* Panel */}
      <div
        style={{
          position: 'fixed',
          top: 0,
          right: isOpen ? 0 : '-800px',
          width: '800px',
          height: '100vh',
          backgroundColor: '#fbf9f8',
          boxShadow: '-4px 0 8px rgba(0, 0, 0, 0.1)',
          zIndex: 1001,
          transition: 'right 0.3s ease',
          display: 'flex',
          flexDirection: 'column',
          border: '1px solid #e5e7eb',
          maxHeight: '100vh',
          overflow: 'hidden'
        }}
      >
        {/* Header */}
        <div className="dashboard-header" style={{
          flexShrink: 0,
          borderBottom: '1px solid #e5e7eb',
          margin: 0,
          backgroundColor: '#fbf9f8'
        }}>
          <div className="dashboard-header-content">
            <p className="dashboard-title" style={{ fontSize: '1.5rem' }}>
              🚀 Flödesloggar
            </p>
            {execution && (
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                fontSize: '0.875rem'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.25rem',
                  color: getStatusColor(execution.status)
                }}>
                  <span>{getStatusIcon(execution.status)}</span>
                  <span style={{ fontWeight: '500', textTransform: 'capitalize' }}>
                    {execution.status}
                  </span>
                </div>
              </div>
            )}
          </div>
          <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
            {execution && (execution.status === 'running' || execution.status === 'pending') && (
              <button
                onClick={handleCancel}
                disabled={cancelling}
                className="action-button secondary"
                style={{
                  backgroundColor: cancelling ? '#f3f4f6' : '#ffffff',
                  color: cancelling ? '#6b7280' : '#ef4444',
                  border: '1px solid #ef4444',
                  opacity: cancelling ? 0.5 : 1,
                  cursor: cancelling ? 'not-allowed' : 'pointer'
                }}
              >
                <span>{cancelling ? '⏳ Avbryter...' : '⏹️ Avbryt'}</span>
              </button>
            )}
            <button
              onClick={onClose}
              className="action-button secondary"
            >
              <span>✕ Stäng</span>
            </button>
          </div>
        </div>

        {/* Logs Content */}
        <div style={{
          flex: 1,
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: '#ffffff'
        }}>
          {!execution ? (
            <div className="empty-state">
              <div className="empty-state-icon">📋</div>
              <h3 className="empty-state-title">Ingen körning aktiv</h3>
              <p className="empty-state-subtitle">Starta ett flöde för att se loggar här</p>
            </div>
          ) : loading && logs.length === 0 ? (
            <div className="empty-state">
              <div className="empty-state-icon">⏳</div>
              <h3 className="empty-state-title">Laddar loggar...</h3>
              <p className="empty-state-subtitle">Väntar på loggdata</p>
            </div>
          ) : logs.length === 0 ? (
            <div className="empty-state">
              <div className="empty-state-icon">📝</div>
              <h3 className="empty-state-title">Inga loggar än</h3>
              <p className="empty-state-subtitle">Loggar kommer att visas här när flödet körs</p>
            </div>
          ) : (
            <div
              className="custom-scrollbar"
              style={{
                flex: 1,
                overflowY: 'auto',
                padding: '1rem'
              }}>
              {logs.map((log, index) => (
                <div
                  key={index}
                  className="card"
                  style={{
                    marginBottom: '0.75rem',
                    borderLeft: `4px solid ${getLogLevelColor(log.level)}`,
                    backgroundColor: '#ffffff'
                  }}
                >
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.75rem',
                    marginBottom: '0.5rem'
                  }}>
                    <span style={{ fontSize: '1rem' }}>{getLogLevelBadge(log.level)}</span>
                    <span style={{
                      fontSize: '0.875rem',
                      fontWeight: '600',
                      color: getLogLevelColor(log.level),
                      textTransform: 'uppercase'
                    }}>
                      {log.level}
                    </span>
                    <span style={{
                      fontSize: '0.75rem',
                      color: '#6b7280',
                      fontFamily: 'Monaco, Consolas, "Courier New", monospace'
                    }}>
                      {formatTimestamp(log.timestamp)}
                    </span>
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    color: '#1a0f0f',
                    lineHeight: '1.5',
                    wordBreak: 'break-word',
                    marginBottom: log.data ? '0.5rem' : '0'
                  }}>
                    {log.message}
                  </div>
                  {log.data && (
                    <div style={{
                      fontSize: '0.75rem',
                      color: '#6b7280',
                      fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                      backgroundColor: '#f2e8e8',
                      padding: '0.75rem',
                      borderRadius: '0.5rem',
                      overflow: 'auto',
                      border: '1px solid #e5e7eb'
                    }}>
                      <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                        {JSON.stringify(log.data, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              ))}
              <div ref={logsEndRef} />
            </div>
          )}
        </div>
      </div>
    </>
  )
}
